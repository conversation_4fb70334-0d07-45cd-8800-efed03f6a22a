# Pollen - 学术论文 Typst 模板

![Typst](https://img.shields.io/badge/Typst-v0.11+-blue)
![License](https://img.shields.io/badge/License-MIT-green)

一个专为中文学术论文设计的现代化 Typst 模板，遵循学术规范，支持完整的论文写作流程。

## ✨ 特色功能

- 🎯 **符合学术规范**：支持中文学术论文格式要求
- 📖 **完整文档结构**：从封面到参考文献的完整框架
- 🎨 **美观排版**：现代化的页面设计和字体配置
- 📚 **智能目录**：自动生成多级目录，支持交叉引用
- 🔗 **文献管理**：集成 BibTeX 参考文献管理
- ⚡ **快速编译**：基于 Typst 的高效编译引擎
- 🎪 **模块化设计**：清晰的文件结构，易于维护和扩展

## 📁 项目结构

```
pollen/
├── settings/            # 核心配置目录
│   ├── main.typ        # 主入口文件
│   ├── template.typ    # 全局样式模板
│   ├── heading.typ     # 标题样式定义
│   ├── toc.typ         # 目录样式配置
│   └── table.typ       # 表格样式设置
├── chapters/           # 章节内容目录
│   ├── abstract.typ    # 摘要（中英文）
│   ├── chapter_template.typ  # 章节模板
│   ├── chapter01.typ   # 第一章：绪论
│   ├── chapter02.typ   # 第二章：相关工作
│   ├── chapter03.typ   # 第三章：方法设计
│   ├── chapter04.typ   # 第四章：实验分析
│   ├── chapter05.typ   # 第五章：总结展望
│   └── references.typ  # 参考文献
├── assets/             # 资源文件
│   └── data/          # 数据文件
│       └── result.csv
├── pollen.bib         # 参考文献数据库
├── pollen.code-workspace  # VS Code 工作空间配置
└── readme.md          # 项目说明文档
```

## 🚀 快速开始

### 环境要求

- [Typst](https://typst.app/) v0.11.0+
- VS Code + [Typst LSP 扩展](https://marketplace.visualstudio.com/items?itemName=nvarner.typst-lsp)（推荐）

### 使用步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd pollen
   ```

2. **编辑论文内容**
   - 在 `settings/main.typ` 中修改论文标题、作者信息
   - 在 `chapters/` 目录下编辑各章节内容
   - 在 `pollen.bib` 中添加参考文献

3. **编译论文**
   ```bash
   typst compile settings/main.typ output.pdf
   ```

## 📝 章节编写指南

### 基本结构

每个章节文件需要遵循以下结构：

```typst
// 导入标题样式（必须）
#import "../settings/heading.typ": *

// 章节开始（重置计数器）
#new-chapter()
#chapter[第X章 章节标题]

#section[小节标题]
这里是正文内容...

#subsection[子小节标题]
更详细的内容...

#subsubsection[四级标题示例]
具体的实现细节...
```

### 标题层级系统

| 标题级别 | 函数调用 | 编号格式 | 示例 | 特点 |
|----------|----------|----------|------|------|
| 一级标题 | `#chapter[...]` | 第X章 | 第1章 绪论 | 手动编号，居中显示 |
| 二级标题 | `#section[...]` | X.Y | 1.1 研究背景 | 自动编号，加粗显示 |
| 三级标题 | `#subsection[...]` | X.Y.Z | 1.1.1 国内现状 | 自动编号，进入目录 |
| 四级标题 | `#subsubsection[...]` | (a)、(b) | (a) 具体方法 | 字母编号，不进入目录 |

### 图表处理

```typst
// 插入图片
#figure(
  image("../assets/images/example.png", width: 80%),
  caption[图像标题说明]
)

// 插入表格
#figure(
  table(
    columns: 3,
    [列1], [列2], [列3],
    [数据1], [数据2], [数据3]
  ),
  caption[表格标题说明]
)
```

### 参考文献引用

```typst
// 正文中引用文献
这是一个重要发现@author2023research。

// 多个文献引用
相关研究表明@smith2022, @jones2023。

// 在 pollen.bib 中添加文献条目
@article{author2023research,
  title = {研究标题},
  author = {作者姓名},
  journal = {期刊名称},
  year = {2023}
}
```

## 🔧 高级配置

### 页面设置

在 `settings/template.typ` 中可配置：
- 页面边距：`margin: (top: 3cm, bottom: 2.5cm, left: 2.5cm, right: 2.5cm)`
- 字体设置：`font: ("STIX Two Text", "Noto Serif CJK SC")`
- 行距设置：`leading: 20pt`
- 首行缩进：`first-line-indent: 3em`

### 页眉页脚

- **页眉**：统一显示"复旦大学博士学位论文"
- **页脚**：
  - 摘要部分：罗马数字页码（I, II, III...）
  - 正文部分：阿拉伯数字页码（1, 2, 3...）

### 目录定制

在 `settings/toc.typ` 中配置目录样式：
- 一级标题：居中，16pt，加粗
- 二级标题：左对齐，14pt，加粗
- 三级标题：缩进，14pt，正常字重

## 📚 模板特性

### 文档结构控制

- **封面**：无页眉页脚
- **目录**：无页眉页脚
- **摘要**：添加页眉，罗马数字页码
- **正文**：完整页眉页脚，阿拉伯数字页码
- **参考文献**：继续正文页码

### 引用格式

- 遵循 GB/T 7714-2015 标准
- 正文中引用显示为上角标
- 自动生成符合规范的参考文献列表

## 🛠️ 常见问题

**Q: 如何添加新章节？**
A: 复制 `chapters/chapter_template.typ`，重命名后在 `settings/main.typ` 中添加 `#include` 语句。

**Q: 如何自定义字体？**
A: 在 `settings/template.typ` 的 `set text` 中修改 `font` 参数。

**Q: 图片无法显示？**
A: 确保图片路径正确，建议将图片放在 `assets/images/` 目录下。

**Q: 参考文献格式不正确？**
A: 检查 `pollen.bib` 文件格式，确保遵循 BibTeX 标准。

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个模板！

---

**提示**：首次使用建议复制 `chapter_template.typ` 作为新章节的起始模板，这样可以确保格式的一致性。