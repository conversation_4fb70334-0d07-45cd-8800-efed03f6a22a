// ================================================================
//                    表格处理文件 (Table Processing)
// ================================================================

// --- 全局表格计数器 ---
#let table-counter = counter("global-table")

// --- 简化三线表样式设置 ---
#let three-line-table(
  data,
  caption: none
) = {
  // 步进表格计数器
  table-counter.step()
  
  // 设置表格样式
  set text(size: 10.5pt)
  
  // 先显示标题
  context [
    #align(center)[
      #text(weight: "bold")[表 #table-counter.display("1"): #caption]
    ]
  ]
  
  v(0.5em)
  
  // 再显示表格
  align(center)[
    #table(
      columns: data.at(0).len(),
      align: center,
      table.header(
        table.hline(stroke: 0.8pt),  // 表头顶部粗线
        ..data.at(0).map(cell => strong(cell)),  // 表头内容
        table.hline(stroke: 0.5pt),  // 表头下细线
      ),
      stroke: (x, y) => {
        if y == data.len() - 1 { (bottom: 0.8pt) } // 表格底部粗线
        else { none }                               // 其他位置无线
      },
      inset: 8pt,
      
      // 数据行
      ..data.slice(1).flatten()
    )
  ]
}

// --- CSV读取函数 ---
#let load-csv-simple(
  file-path,
  caption: none
) = {
  let data = csv(file-path)
  three-line-table(data, caption: caption)
}

// --- 示例：result.csv数据表格 ---
#let result-table(caption: "实验结果数据") = {
  load-csv-simple("../assets/data/result.csv", caption: caption)
}

// --- 手动创建表格 ---
#let manual-table(
  headers,
  rows,
  caption: none
) = {
  let data = (headers,) + rows
  three-line-table(data, caption: caption)
}
