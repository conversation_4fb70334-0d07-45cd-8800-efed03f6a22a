// ================================================================
//                   第二章 基于YOLO v11的空气致敏花粉颗粒检测方法开发
// ================================================================
#import "../settings/heading.typ": *
#import "../settings/table.typ": *


#new-chapter()
#chapter[第二章 基于YOLO v11的空气致敏花粉颗粒检测方法开发]



// ================================================================
//                   2.1 引言
// ================================================================
#section[引言]
引言测试@LiuChangHaiJiYuShuJuZengQiangHeTeZhengRongHeDeHuaFenTuXiangFenLei2025@WangYaQunJiYuShuJuZengGuangHeTeZhengRongHeDeHuaFenXiBaoTuXiangShiBie2020@ZhangXinYueJiYuTuXiangChuLiDeHuaFenShenDuTuXiangXiuZhengFangFaYanJiu2023


本章将介绍与研究相关的技术背景...



// ================================================================
//                   2.2 材料与方法
// ================================================================
#section[材料与方法]

#subsection[实验材料与仪器]

详细介绍技术原理A...




// ================================================================
//                   2.2.2 花粉样本制备与数据采集
// ================================================================
#subsection[花粉样本制备与显微图像数据采集]
#subsubsection[花粉样本载玻片制备方法]
详细介绍技术原理B...
#subsubsection[显微图像采集参数优化]

详细介绍技术原理B...
#subsubsection[图像质量控制标准]
详细介绍技术原理B...
// ================================================================
//                   2.2.3 数据集构建与标注
// ================================================================
#subsection[数据集构建与标注]
#subsubsection[原始图像预处理]

详细介绍技术原理B...

#subsubsection[人工标注规范与流程]

详细介绍技术原理B...
#subsubsection[数据集划分策略]

详细介绍技术原理B...
#subsubsection[数据集增强方法]

详细介绍技术原理B...



// ================================================================
//                   2.2.4 YOLO V11模型训练与优化
// ================================================================
#subsection[基于YOLO v11的目标检测模型]

#subsubsection[模型选择与网络架构配置]

详细介绍技术原理B...

#subsubsection[超参数选择与调优]
详细介绍技术原理B...

#subsubsection[损失函数设计]

详细介绍技术原理B...
#subsubsection[训练策略与收敛分析]

详细介绍技术原理B...
// ================================================================
//                   2.2.5 对照实验设计
// ================================================================
#subsection[实验设计与性能评估]


#subsubsection[人类专家识别流程]
详细介绍技术原理B...

#subsubsection[染色对照组试验方法]

详细介绍技术原理B...
#subsubsection[评价指标体系建立]
详细介绍技术原理B...
// ================================================================
//                   第三节 结果与讨论
// ================================================================
#section[结果与讨论]


// ================================================================
//                   2.3.1 数据集特征分析
// ================================================================
#subsection[模型训练与验证结果分析]

#subsubsection[14种花粉形态学特征统计]
详细介绍技术原理B...
详细介绍技术原理B...详细介绍技术原理B...详细介绍技术原理B...
详细介绍技术原理B...
#load-csv-simple("../assets/data/result.csv", caption: "实验结果")




#subsubsection[地域性差异分析]

详细介绍技术原理B...
#subsubsection[图像特征分布可视化]
详细介绍技术原理B...


// ================================================================
//                   2.3.2 模型性能评估
// ================================================================

#subsection[无染色混合样本检测性能评估]

#subsubsection[训练过程监控与收敛分析]
详细介绍技术原理B...

#subsubsection[精确度-召回率(P-R)曲线分析]

详细介绍技术原理B...
#subsubsection[F1-置信度曲线评估]
详细介绍技术原理B...

#subsubsection[FROC曲线性能分析]
详细介绍技术原理B...


#subsubsection[综合性能评价]

// ================================================================
//                   2.3.3 分类检测结果分析
// ================================================================

#subsection[分类检测结果分析]


#subsubsection[单一花粉检测准确率]
详细介绍技术原理B...

#subsubsection[混合样本检测性能]

详细介绍技术原理B...
#subsubsection[混淆矩阵与错误分析]
详细介绍技术原理B...

#subsubsection[小目标花粉检测挑战]
详细介绍技术原理B...




// ================================================================
//                   2.3.4 方法对比研究
// ================================================================
#subsection[模型与人类专家检测结果的深度对比分析]


#subsubsection[算法与人工专家检测对比]
详细介绍技术原理B...

#subsubsection[无染色方法与染色方法对比]

详细介绍技术原理B...
#subsubsection[检测效率与成本分析]
详细介绍技术原理B...

#subsubsection[结果一致性评价]
详细介绍技术原理B...


// ================================================================
//                   2.3.5 模型鲁棒性与泛化能力
// ================================================================
#subsection[方法学创新性的综合讨论]

#subsubsection[不同地域样本的交叉验证]
详细介绍技术原理B...

#subsubsection[图像质量对检测的影响]

详细介绍技术原理B...
#subsubsection[背景复杂度影响分析]
详细介绍技术原理B...

#subsubsection[边缘案例讨论]
详细介绍技术原理B...




本研究中使用的主要工具包括...



// ================================================================
//                   第四节 方法优势与局限性分析
// ================================================================
#section[方法优势与局限性分析]


#subsection[无染色检测的技术优势]



#subsection[实时检测能力评估]


#subsection[当前方法的局限性]


#subsection[改进方向与优化策略]


// ================================================================
//                   第四节 方法优势与局限性分析
// ================================================================
#section[本章小结]

当前技术的发展趋势分析...
